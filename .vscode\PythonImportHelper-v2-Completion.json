[{"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "verify_relationship_fields", "kind": 2, "importPath": "verify_relationship_fields", "description": "verify_relationship_fields", "peekOfCode": "def verify_relationship_fields():\n    \"\"\"验证 relationships.parquet 中的字段结构\"\"\"\n    print(\"🔍 验证 GraphRAG relationships.parquet 文件结构\")\n    print(\"=\" * 60)\n    # 读取 relationships.parquet\n    relationships_df = pd.read_parquet('output/relationships.parquet')\n    print(f\"📊 Relationships.parquet 文件信息:\")\n    print(f\"  文件路径: output/relationships.parquet\")\n    print(f\"  行数: {len(relationships_df)}\")\n    print(f\"  列数: {len(relationships_df.columns)}\")", "detail": "verify_relationship_fields", "documentation": {}}, {"label": "compare_with_entities", "kind": 2, "importPath": "verify_relationship_fields", "description": "verify_relationship_fields", "peekOfCode": "def compare_with_entities():\n    \"\"\"比较 relationships.parquet 和 entities.parquet 的一致性\"\"\"\n    print(f\"\\n🔄 比较实体和关系数据的一致性:\")\n    entities_df = pd.read_parquet('output/entities.parquet')\n    relationships_df = pd.read_parquet('output/relationships.parquet')\n    # 从实体文件获取所有实体\n    entity_names = set(entities_df['title'].unique())\n    # 从关系文件获取所有节点\n    if 'source' in relationships_df.columns and 'target' in relationships_df.columns:\n        relationship_nodes = set(relationships_df['source'].unique()) | set(relationships_df['target'].unique())", "detail": "verify_relationship_fields", "documentation": {}}]