# GraphRAG 知识图谱构建与 Gephi 可视化 PRD

## 1. 项目概述

### 1.1 项目目标

使用 Microsoft GraphRAG 框架从文档中构建知识图谱，并通过 Gephi 进行可视化分析。

### 1.2 核心价值

- 从非结构化文本中提取结构化知识
- 构建实体关系网络
- 提供直观的图谱可视化
- 支持复杂查询和分析

## 2. 技术架构

### 2.1 核心组件

- **GraphRAG**: Microsoft 开源的知识图谱构建框架
- **Gephi**: 开源网络分析和可视化软件
- **LLM**: 大语言模型（通过 OpenRouter API 访问）
- **Python 环境**: 运行 GraphRAG 的基础环境

### 2.2 数据流程

```
原始文档 → GraphRAG处理 → 知识图谱 → 导出格式 → Gephi可视化
```

## 3. 实施步骤

### 3.1 环境准备

#### 3.1.1 安装 GraphRAG

```bash
# 创建虚拟环境（已完成）
python -m venv .venv
.venv\Scripts\activate  # Windows 激活虚拟环境

# 安装GraphRAG（已完成）
pip install graphrag
```

#### 3.1.2 安装 Gephi

- 下载地址: https://gephi.org/
- 支持 Windows、macOS、Linux
- 需要 Java 8+环境

### 3.2 项目初始化

#### 3.2.1 创建项目目录

```bash
# 项目目录已存在：C:\Users\<USER>\Desktop\graphrag
# 当前工作目录即为项目根目录
```

#### 3.2.2 初始化 GraphRAG

```bash
python -m graphrag.index --init --root .
```

#### 3.2.3 目录结构

```
C:\Users\<USER>\Desktop\graphrag/
├── .venv/                  # 虚拟环境（已创建）
├── .env                    # 环境变量配置
├── settings.yaml          # GraphRAG配置文件
├── input/                  # 输入文档目录
│   └── 第八章.md           # 输入文档
├── output/                 # 输出结果目录
└── 第八章.md               # 原始文档文件
```

### 3.3 配置设置

#### 3.3.1 OpenRouter API 说明

本项目使用 OpenRouter API 来访问 GPT-4.1 模型，OpenRouter 提供了统一的 API 接口来访问多种大语言模型：

- **优势**: 支持多种模型、价格透明、无需多个 API 密钥
- **模型**: 使用 `openai/gpt-4.1` 获得最佳的实体抽取和关系识别效果
- **API 兼容**: 完全兼容 OpenAI API 格式

#### 3.3.2 环境变量配置 (.env)

```env
# OpenRouter API配置
GRAPHRAG_API_KEY=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
GRAPHRAG_API_BASE=https://openrouter.ai/api/v1

```

#### 3.3.3 基础配置 (settings.yaml)

```yaml
llm:
  api_key: ${GRAPHRAG_API_KEY}
  api_base: ${GRAPHRAG_API_BASE}
  type: openai_chat
  model: openai/gpt-4.1
  max_tokens: 4000
  temperature: 0

embeddings:
  llm:
    api_key: ${GRAPHRAG_API_KEY}
    api_base: ${GRAPHRAG_API_BASE}
    type: openai_embedding
    model: text-embedding-3-small

input:
  type: file
  file_type: text
  base_dir: "input"
  file_encoding: utf-8

storage:
  type: file
  base_dir: "output"

cache:
  type: file
  base_dir: "cache"

reporting:
  type: file
  base_dir: "reporting"
```

### 3.4 数据准备

#### 3.4.1 准备输入文档

- 将待分析的文档放入 `input/` 目录
- 支持格式：.txt, .md, .docx, .pdf
- 建议单个文件不超过 10MB

#### 3.4.2 文档预处理建议

- 确保文档编码为 UTF-8
- 移除不必要的格式标记
- 保持文本结构清晰

### 3.5 构建知识图谱

#### 3.5.1 运行索引构建

```bash
python -m graphrag.index --root .
```

#### 3.5.2 监控处理进度

- 查看 `reporting/` 目录下的日志文件
- 处理时间取决于文档大小和复杂度

#### 3.5.3 输出文件说明

```
output/
├── artifacts/
│   ├── create_final_entities.parquet      # 实体数据
│   ├── create_final_relationships.parquet # 关系数据
│   ├── create_final_communities.parquet   # 社区数据
│   └── create_final_nodes.parquet         # 节点数据
└── graph.graphml                          # GraphML格式图谱
```
