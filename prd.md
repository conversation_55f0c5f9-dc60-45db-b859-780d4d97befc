# GraphRAG 知识图谱构建与可视化 PRD

## 1. 项目概述

### 1.1 项目目标

使用 Microsoft GraphRAG 框架从文档中构建知识图谱，生成包含关系类型字段的结构化数据，并通过 Web 可视化进行分析展示。

### 1.2 核心价值

- 从非结构化文本中提取结构化知识
- 构建包含关系类型的实体关系网络
- 提供直观的 Web 图谱可视化
- 支持复杂查询和分析
- 生成标准化的关系类型分类

## 2. 技术架构

### 2.1 核心组件

- **GraphRAG**: Microsoft 开源的知识图谱构建框架
- **Web 可视化**: 基于 HTML/JavaScript 的图谱可视化界面
- **LLM**: 大语言模型（通过 OpenAI 官方 API 访问）
- **Python 环境**: 运行 GraphRAG 的基础环境
- **自定义提示系统**: 用于生成关系类型字段的提示配置

### 2.2 数据流程

```
原始文档 → GraphRAG处理（含关系类型提取） → 知识图谱 → 包含relationship_type字段的parquet文件 → Web可视化
```

### 2.3 关系类型增强方案

为了在 GraphRAG 生成的 parquet 文件中包含 relationship_type 字段，需要实施以下技术方案：

- **自定义实体关系提取提示**: 修改 GraphRAG 的实体关系提取提示，明确要求 LLM 识别并分类关系类型
- **关系类型分类体系**: 建立标准化的关系类型分类，如"因果关系"、"从属关系"、"时间关系"等
- **输出格式定制**: 配置 GraphRAG 输出格式，确保关系数据包含类型字段
- **后处理增强**: 对生成的关系数据进行后处理，补充和标准化关系类型信息

## 3. 实施步骤

### 3.1 环境准备

#### 3.1.1 安装 GraphRAG

```bash
# 创建虚拟环境（已完成）
python -m venv .venv
.venv\Scripts\activate  # Windows 激活虚拟环境

# 安装GraphRAG（已完成）
pip install graphrag
```

### 3.2 项目初始化

#### 3.2.1 创建项目目录

```bash
# 项目目录已存在：C:\Users\<USER>\Desktop\graphrag
# 当前工作目录即为项目根目录
```

#### 3.2.2 初始化 GraphRAG

```bash
python -m graphrag.index --init --root .
```

#### 3.2.3 目录结构

```
C:\Users\<USER>\Desktop\graphrag/
├── .venv/                  # 虚拟环境（已创建）
├── .env                    # 环境变量配置
├── settings.yaml          # GraphRAG配置文件
├── input/                  # 输入文档目录
│   └── 第八章.md           # 输入文档
├── output/                 # 输出结果目录
└── 第八章.md               # 原始文档文件
```

### 3.3 配置设置

#### 3.3.1 OpenAI API 说明

本项目使用 OpenAI 官方 API 来访问 GPT-4.1 模型，以获得最佳的实体抽取和关系识别效果：

- **优势**: 官方 API 稳定性高、响应速度快、支持最新模型特性
- **模型**: 使用 `gpt-4.1-2025-04-14` 获得最佳的实体抽取和关系类型识别效果
- **嵌入模型**: 使用 `text-embedding-3-large` 获得高质量的文本嵌入

#### 3.3.2 环境变量配置 (.env)

```env
# OpenAI API配置
GRAPHRAG_API_KEY=your_openai_api_key_here
GRAPHRAG_API_BASE=https://api.openai.com/v1

```

#### 3.3.3 基础配置 (settings.yaml)

```yaml
models:
  default_chat_model:
    api_key: ${GRAPHRAG_API_KEY}
    api_base: ${GRAPHRAG_API_BASE}
    type: openai_chat
    model: gpt-4.1-2025-04-14
    max_tokens: 4000
    temperature: 0
    model_supports_json: true

  default_embedding_model:
    api_key: ${GRAPHRAG_API_KEY}
    api_base: ${GRAPHRAG_API_BASE}
    type: openai_embedding
    model: text-embedding-3-large

input:
  type: file
  file_type: text
  base_dir: "input"
  file_encoding: utf-8

output:
  type: file
  base_dir: "output"

cache:
  type: file
  base_dir: "cache"

reporting:
  type: file
  base_dir: "reporting"

# 关系类型增强配置
extract_graph:
  model_id: default_chat_model
  prompt: "prompts/entity_extraction_with_types.txt"
  entity_types: ["人物", "组织", "地点", "事件", "概念", "时间"]
  max_gleanings: 1
```

### 3.4 关系类型提示配置

#### 3.4.1 创建自定义提示文件

创建 `prompts/entity_extraction_with_types.txt` 文件，包含以下关键要素：

- **实体识别指令**: 明确要求识别特定类型的实体
- **关系类型分类**: 定义标准化的关系类型分类体系
- **输出格式规范**: 指定包含 relationship_type 字段的输出格式
- **中文优化**: 针对中文文本的特殊处理指令

#### 3.4.2 关系类型分类体系

基于 Neo4j 数据库的关系类型命名规范，建立以下标准化关系类型分类体系：

**命名规范**：

- 关系类型使用全大写字母
- 使用下划线分隔单词
- 采用动词或动词短语形式
- 具有明确的语义和方向性

**标准关系类型**：

**组织关系类**：

- `WORKS_FOR`: 工作关系（人员-组织）
- `MANAGES`: 管理关系（上级-下级）
- `BELONGS_TO`: 从属关系（部门-组织）
- `REPORTS_TO`: 汇报关系（下级-上级）

**位置关系类**：

- `LOCATED_IN`: 位置关系（实体-地点）
- `CONTAINS`: 包含关系（容器-内容）
- `ADJACENT_TO`: 邻接关系（地点-地点）
- `CONNECTS_TO`: 连接关系（路径-地点）

**时间关系类**：

- `OCCURS_BEFORE`: 时间先后关系
- `OCCURS_AFTER`: 时间后续关系
- `OCCURS_DURING`: 时间包含关系
- `STARTS_WITH`: 开始关系
- `ENDS_WITH`: 结束关系

**因果关系类**：

- `CAUSES`: 直接因果关系
- `LEADS_TO`: 导致关系
- `RESULTS_FROM`: 结果关系
- `INFLUENCES`: 影响关系

**协作关系类**：

- `COLLABORATES_WITH`: 协作关系
- `PARTNERS_WITH`: 合作伙伴关系
- `SUPPORTS`: 支持关系
- `ASSISTS`: 协助关系

**对立关系类**：

- `OPPOSES`: 对立关系
- `CONFLICTS_WITH`: 冲突关系
- `COMPETES_WITH`: 竞争关系
- `CONTRADICTS`: 矛盾关系

**相似关系类**：

- `SIMILAR_TO`: 相似关系
- `EQUIVALENT_TO`: 等价关系
- `RELATED_TO`: 相关关系
- `ASSOCIATED_WITH`: 关联关系

### 3.5 数据准备

#### 3.5.1 准备输入文档

- 将待分析的文档放入 `input/` 目录
- 支持格式：.txt, .md, .docx, .pdf
- 建议单个文件不超过 10MB

#### 3.5.2 文档预处理建议

- 确保文档编码为 UTF-8
- 移除不必要的格式标记
- 保持文本结构清晰

### 3.6 自定义提示文件创建

#### 3.6.1 实体关系提取提示模板

创建包含 relationship_type 字段的自定义提示文件，指导 LLM 按照 Neo4j 标准生成关系类型：

**提示要点**：

- 明确要求输出包含 relationship_type 字段
- 提供标准关系类型列表供 LLM 参考
- 指定输出格式为结构化数据
- 强调关系方向性和语义准确性

#### 3.6.2 中文关系类型映射

建立中文描述到英文标准关系类型的映射规则：

- "工作于" → `WORKS_FOR`
- "管理" → `MANAGES`
- "位于" → `LOCATED_IN`
- "导致" → `CAUSES`
- "合作" → `COLLABORATES_WITH`

### 3.7 构建知识图谱

#### 3.7.1 运行索引构建

```bash
python -m graphrag.index --root .
```

#### 3.7.2 监控处理进度

- 查看 `reporting/` 目录下的日志文件
- 处理时间取决于文档大小和复杂度
- 关注关系类型提取的准确性

#### 3.7.3 输出文件说明

```
output/
├── artifacts/
│   ├── create_final_entities.parquet      # 实体数据
│   ├── create_final_relationships.parquet # 关系数据（含relationship_type字段）
│   ├── create_final_communities.parquet   # 社区数据
│   └── create_final_nodes.parquet         # 节点数据
└── graph.graphml                          # GraphML格式图谱
```

**关系数据结构增强**：

- `source`: 源实体 ID
- `target`: 目标实体 ID
- `description`: 关系描述
- `weight`: 关系权重
- `relationship_type`: 标准化关系类型（新增字段）

### 3.8 数据验证与质量控制

#### 3.8.1 关系类型验证

- 检查生成的关系类型是否符合 Neo4j 命名规范
- 验证关系类型的语义准确性
- 统计各类关系类型的分布情况

#### 3.8.2 数据质量评估

- 评估实体识别的准确率
- 检查关系抽取的完整性
- 验证关系方向的正确性

## 4. 可视化方案

### 4.1 Web 可视化架构

- 使用 HTML/JavaScript 构建交互式图谱界面
- 支持关系类型的颜色编码和样式区分
- 提供关系类型筛选和搜索功能

### 4.2 关系类型可视化

- 不同关系类型使用不同颜色和线型
- 添加方向箭头显示关系方向
- 支持关系类型图例和说明

## 5. 预期成果

### 5.1 技术成果

- 生成包含 relationship_type 字段的标准化 parquet 文件
- 建立符合 Neo4j 规范的关系类型分类体系
- 实现中文文本的准确关系类型识别

### 5.2 应用价值

- 提升知识图谱的语义表达能力
- 支持更精确的图谱查询和分析
- 为后续图数据库导入提供标准化数据格式
