# — Goal —
从下列文本中抽取三元组，并且为每条边打上"关系类型"。
允许的关系类型只有：
BELONGS_TO, IS_A, RELATED_TO, TEACHES, PREREQUISITE_OF,
PART_OF, CAUSES, LOCATED_IN, MENTIONED_IN, WORKS_FOR,
MANAGES, CONTAINS, OCCURS_BEFORE, LEADS_TO, COLLABORATES_WITH,
OPPOSES, SIMILAR_TO, MENTIONS, CITES, AUTHORED_BY, PUBLISHED_IN,
DERIVED_FROM, HAS_TOPIC, USES, EXTENDS, HAS_PROPERTY

# — 格式 —
("relationship"{tuple_delimiter}"<head>"{tuple_delimiter}"<tail>"
{tuple_delimiter}"<relation_type>"{tuple_delimiter}"<description>"
{tuple_delimiter}<strength>){record_delimiter}

{completion_delimiter}

# — 输入 —
{text_unit_delimiter}
{input_text}
